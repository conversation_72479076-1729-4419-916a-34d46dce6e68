import {
  Box,
  Icon,
  Icon<PERSON>utton,
  useBreakpointValue,
  useDisclosure,
} from '@chakra-ui/react';
import {
  useHasCashierAnyBonus,
  useIsCashierBonusEnabled,
} from 'modules/applications/list/controls/hooks';
import { CreditEligibilityModal } from 'modules/applications/shared/components/credit-eligibility-modal';
import {
  Terminal,
  TerminalControlsDesktop,
  useTerminalForm,
} from 'modules/terminal';
import { CashierBonus } from 'modules/terminal/components/cashier-bonus';

import { PracticeModeDisclaimer } from 'modules/terminal/components/PracticeModeDisclaimer';
import { FormProvider } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiCheckCircle, FiRotateCw } from 'react-icons/fi';
import {
  ConfirmationModal,
  Header,
  Loader,
  PracticeOffIcon,
  PracticeOnIcon,
} from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { usePageTitle } from 'shared/hooks/app';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { useMerchantDetails } from 'shared/hooks/merchant';

const TerminalPage = () => {
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);
  const { t } = useTranslation(LocizeNamespaces.TERMINAL);
  const formMethods = useTerminalForm();
  const { loading } = useMerchantDetails();
  const isMobileLayout = useIsMobileLayout();
  const shouldHaveSmallerIcons = useBreakpointValue({ base: true, sm: false });
  const isCashierBonusEnabled = useIsCashierBonusEnabled();
  const hasCashierAnyBonus = useHasCashierAnyBonus();
  const { watch, setValue, reset: handleFormReset } = formMethods;
  const isPracticeMode = watch('isPracticeMode');
  const {
    isOpen: isCreditModalOpen,
    onOpen: onCreditModalOpen,
    onClose: onCreditModalClose,
  } = useDisclosure();

  usePageTitle(tc(LocizeCommonKeys.SIDEBAR_TERMINAL_OPTION));

  if (loading) {
    return <Loader />;
  }

  const boxSize = shouldHaveSmallerIcons ? 10 : 14;
  const iconSize = shouldHaveSmallerIcons ? 4 : 6;

  return (
    <FormProvider {...formMethods}>
      {isMobileLayout ? (
        <Header
          after={
            <Box alignItems="center" display="flex" ml="auto" mr={1}>
              {isCashierBonusEnabled && hasCashierAnyBonus && <CashierBonus />}
              <IconButton
                minW={shouldHaveSmallerIcons ? 'unset' : 'auto'}
                aria-label="credit-eligibility"
                boxSize={boxSize}
                icon={
                  <Icon
                    as={FiCheckCircle}
                    boxSize={iconSize}
                    color="primary.800"
                  />
                }
                isRound
                onClick={onCreditModalOpen}
                variant="ghost"
              />
              <IconButton
                minW={shouldHaveSmallerIcons ? 'unset' : 'auto'}
                aria-label="reset"
                boxSize={boxSize}
                icon={
                  <Icon
                    as={isPracticeMode ? PracticeOnIcon : PracticeOffIcon}
                    boxSize={iconSize}
                    color="primary.800"
                  />
                }
                isRound
                onClick={() => {
                  setValue('isPracticeMode', !isPracticeMode, {
                    shouldDirty: true,
                  });
                }}
                variant="ghost"
              />
              <ConfirmationModal
                actionText={t('clear-terminal.action')}
                onAction={handleFormReset}
                popoverPlacement="bottom-end"
                title={t('clear-terminal.title')}
                trigger={
                  <IconButton
                    minW="unset"
                    aria-label="reset"
                    boxSize={boxSize}
                    icon={
                      <Icon
                        as={FiRotateCw}
                        boxSize={iconSize}
                        color="primary.800"
                      />
                    }
                    isRound
                    variant="ghost"
                  />
                }
              />
            </Box>
          }
          title={tc(LocizeCommonKeys.SIDEBAR_TERMINAL_OPTION)}
        />
      ) : (
        <TerminalControlsDesktop />
      )}
      <Box px="20px">
        <PracticeModeDisclaimer />
        <Terminal />
      </Box>

      <CreditEligibilityModal
        isOpen={isCreditModalOpen}
        onClose={onCreditModalClose}
      />
    </FormProvider>
  );
};

export default TerminalPage;
