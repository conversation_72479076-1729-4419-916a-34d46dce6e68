import { useContext, useState } from 'react';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { GlobalStateContext } from 'shared/hooks/state';
import { useRudderStack } from 'hooks/use-rudderstack';

import { useUserEligibilityLazyQuery } from './queries.gen';
import {
  type CreditEligibilityCheckResponse,
  type CreditEligibilityFormData,
  CreditEligibilityStatus,
} from './types';

export const useCreditEligibilityCheck = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<
    CreditEligibilityCheckResponse | undefined
  >();
  const handleGenericError = useHandleGenericError();
  const { merchantId } = useContext(GlobalStateContext);
  const [getUserEligibility] = useUserEligibilityLazyQuery();
  const { ruderStackEvents } = useRudderStack();

  const checkEligibility = async (data: CreditEligibilityFormData) => {
    setIsLoading(true);
    try {
      if (!merchantId) {
        throw new Error('Merchant ID is required');
      }

      // Track email filled event
      ruderStackEvents.creditEligibilityEmailFilled({
        merchantId,
        idCode: data.idCode,
        email: data.email,
      });

      // Track consents collected event
      ruderStackEvents.creditEligibilityConsentsCollected({
        merchantId,
        idCode: data.idCode,
        privacyPolicyConsent: data.privacyPolicyConsent,
        newsletterConsent: data.newsletterConsent,
      });

      const response = await getUserEligibility({
        variables: {
          merchant_id: merchantId,
          pin: data.idCode,
        },
      });

      const eligibilityResult = response.data?.user_eligibility;

      // Parse the eligibility result and map to our response format
      const parsedResponse: CreditEligibilityCheckResponse = {
        status:
          eligibilityResult === 'ELIGIBLE'
            ? CreditEligibilityStatus.ELIGIBLE
            : CreditEligibilityStatus.NOT_ELIGIBLE,
        customerName: `Customer (${data.idCode})`,
        message:
          eligibilityResult === 'ELIGIBLE'
            ? 'Client is eligible for this amount! Proceed with application.'
            : `Client is not eligible: ${eligibilityResult}`,
      };

      // Track customer screened event
      ruderStackEvents.creditEligibilityCustomerScreened({
        merchantId,
        idCode: data.idCode,
        amount: data.amount,
        status: parsedResponse.status,
      });

      setResult(parsedResponse);
      return parsedResponse;
    } catch (error) {
      handleGenericError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const resetResult = () => {
    setResult(undefined);
  };

  return {
    isLoading,
    result,
    checkEligibility,
    resetResult,
  };
};
