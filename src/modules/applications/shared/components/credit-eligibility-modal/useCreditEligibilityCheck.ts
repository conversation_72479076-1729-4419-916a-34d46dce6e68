import { useState } from 'react';
import { useHandleGenericError } from 'shared/hooks/alerts';

import {
  type CreditEligibilityCheckResponse,
  type CreditEligibilityFormData,
  CreditEligibilityStatus,
} from './types';

export const useCreditEligibilityCheck = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<
    CreditEligibilityCheckResponse | undefined
  >();
  const handleGenericError = useHandleGenericError();

  const checkEligibility = async (data: CreditEligibilityFormData) => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual GraphQL mutation call
      // For now, simulate API call with mock data
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock response based on amount for demo purposes
      const mockResponse: CreditEligibilityCheckResponse = {
        status:
          data.amount > 1000
            ? CreditEligibilityStatus.NOT_ELIGIBLE
            : data.amount > 500
              ? CreditEligibilityStatus.MORE_INFO_NEEDED
              : CreditEligibilityStatus.ELIGIBLE,
        customerName: `<PERSON> (${data.idCode})`,
        message:
          data.amount > 1000
            ? 'Client is not eligible. Try a lower amount?'
            : data.amount > 500
              ? 'More information needed to determine eligibility. Proceed with application.'
              : 'Client is eligible for this amount! Proceed with application.',
      };

      setResult(mockResponse);
      return mockResponse;
    } catch (error) {
      handleGenericError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const resetResult = () => {
    setResult(undefined);
  };

  return {
    isLoading,
    result,
    checkEligibility,
    resetResult,
  };
};
