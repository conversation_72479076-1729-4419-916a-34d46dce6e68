import {
  Modal,
  <PERSON>dal<PERSON>ody,
  ModalContent,
  Mo<PERSON><PERSON>eader,
  ModalOverlay,
} from '@chakra-ui/react';
import { useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalCloseButton } from 'shared/components';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { GlobalStateContext } from 'shared/hooks/state';
import { useRudderStack } from 'hooks/use-rudderstack';

import { CreditEligibilityForm } from './CreditEligibilityForm';
import {
  type CreditEligibilityFormData,
  type CreditEligibilityModalProps,
} from './types';
import { useCreditEligibilityCheck } from './useCreditEligibilityCheck';

export const CreditEligibilityModal = ({
  isOpen,
  onClose,
}: CreditEligibilityModalProps) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const { isLoading, result, checkEligibility, resetResult } =
    useCreditEligibilityCheck();
  const { merchantId } = useContext(GlobalStateContext);
  const { ruderStackEvents } = useRudderStack();

  // Track modal opened event
  useEffect(() => {
    if (isOpen && merchantId) {
      ruderStackEvents.creditEligibilityModalOpened({ merchantId });
    }
  }, [isOpen, merchantId, ruderStackEvents]);

  const handleSubmit = async (data: CreditEligibilityFormData) => {
    await checkEligibility(data);
  };

  const handleClose = () => {
    resetResult();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="custom"
      isCentered
      scrollBehavior="inside"
    >
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        maxW="640px"
        borderRadius="12px"
        data-cy="credit-eligibility-modal-content"
      >
        <ModalHeader
          fontSize="24px"
          fontWeight="700"
          lineHeight="1.25"
          px={8}
          py={6}
        >
          {t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_TITLE)}
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody px={8} pb={8}>
          <CreditEligibilityForm
            onSubmit={handleSubmit}
            isLoading={isLoading}
            result={result}
            onClose={handleClose}
          />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
