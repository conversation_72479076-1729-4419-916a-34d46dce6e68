/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type DeleteApplicationModificationRequestMutationVariables = Types.Exact<{
  requestId: Types.Scalars['Int']['input'];
}>;


export type DeleteApplicationModificationRequestMutation = { __typename?: 'Mutation', delete_application_modification_request: boolean };


export const DeleteApplicationModificationRequestDocument = gql`
    mutation DeleteApplicationModificationRequest($requestId: Int!) {
  delete_application_modification_request(request_id: $requestId)
}
    `;
export type DeleteApplicationModificationRequestMutationFn = Apollo.MutationFunction<DeleteApplicationModificationRequestMutation, DeleteApplicationModificationRequestMutationVariables>;

/**
 * __useDeleteApplicationModificationRequestMutation__
 *
 * To run a mutation, you first call `useDeleteApplicationModificationRequestMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteApplicationModificationRequestMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteApplicationModificationRequestMutation, { data, loading, error }] = useDeleteApplicationModificationRequestMutation({
 *   variables: {
 *      requestId: // value for 'requestId'
 *   },
 * });
 */
export function useDeleteApplicationModificationRequestMutation(baseOptions?: Apollo.MutationHookOptions<DeleteApplicationModificationRequestMutation, DeleteApplicationModificationRequestMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteApplicationModificationRequestMutation, DeleteApplicationModificationRequestMutationVariables>(DeleteApplicationModificationRequestDocument, options);
      }
export type DeleteApplicationModificationRequestMutationHookResult = ReturnType<typeof useDeleteApplicationModificationRequestMutation>;
export type DeleteApplicationModificationRequestMutationResult = Apollo.MutationResult<DeleteApplicationModificationRequestMutation>;
export type DeleteApplicationModificationRequestMutationOptions = Apollo.BaseMutationOptions<DeleteApplicationModificationRequestMutation, DeleteApplicationModificationRequestMutationVariables>;