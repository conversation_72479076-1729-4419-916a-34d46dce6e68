/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type LoginPasswordMutationVariables = Types.Exact<{
  username: Types.Scalars['String']['input'];
  password: Types.Scalars['String']['input'];
}>;


export type LoginPasswordMutation = { __typename?: 'Mutation', password_login?: boolean | null };


export const LoginPasswordDocument = gql`
    mutation LoginPassword($username: String!, $password: String!) {
  password_login(username: $username, password: $password)
}
    `;
export type LoginPasswordMutationFn = Apollo.MutationFunction<LoginPasswordMutation, LoginPasswordMutationVariables>;

/**
 * __useLoginPasswordMutation__
 *
 * To run a mutation, you first call `useLoginPasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLoginPasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [loginPasswordMutation, { data, loading, error }] = useLoginPasswordMutation({
 *   variables: {
 *      username: // value for 'username'
 *      password: // value for 'password'
 *   },
 * });
 */
export function useLoginPasswordMutation(baseOptions?: Apollo.MutationHookOptions<LoginPasswordMutation, LoginPasswordMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<LoginPasswordMutation, LoginPasswordMutationVariables>(LoginPasswordDocument, options);
      }
export type LoginPasswordMutationHookResult = ReturnType<typeof useLoginPasswordMutation>;
export type LoginPasswordMutationResult = Apollo.MutationResult<LoginPasswordMutation>;
export type LoginPasswordMutationOptions = Apollo.BaseMutationOptions<LoginPasswordMutation, LoginPasswordMutationVariables>;